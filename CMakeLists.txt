cmake_minimum_required(VERSION 3.10)
project(MSHNet_TensorRT_Test)

set(CMAKE_CXX_STANDARD 17)

# 查找 OpenCV
find_package(OpenCV REQUIRED)
message(STATUS "OpenCV include: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV libs: ${OpenCV_LIBS}")

# 查找 CUDA
find_package(CUDA REQUIRED)
message(STATUS "CUDA include: ${CUDA_INCLUDE_DIRS}")
message(STATUS "CUDA libs: ${CUDA_LIBRARIES}")

# 手动配置 TensorRT 路径
set(TENSORRT_ROOT "$ENV{TENSORRT_ROOT}" CACHE PATH "TensorRT 安装路径")
message(STATUS "TensorRT root: ${TENSORRT_ROOT}")

# 查找 TensorRT 头文件和库
find_path(TENSORRT_INCLUDE_DIR NvInfer.h
    PATHS ${TENSORRT_ROOT}/include
    REQUIRED
)
find_library(TENSORRT_LIB nvinfer
    PATHS ${TENSORRT_ROOT}/lib
    REQUIRED
)
find_library(NVINFER_PLUGIN_LIB nvinfer_plugin
    PATHS ${TENSORRT_ROOT}/lib
    REQUIRED
)

message(STATUS "TensorRT include: ${TENSORRT_INCLUDE_DIR}")
message(STATUS "TensorRT lib: ${TENSORRT_LIB}")
message(STATUS "NVIDIA Plugin lib: ${NVINFER_PLUGIN_LIB}")

# 自动收集源文件
file(GLOB_RECURSE SOURCES "src/*.cpp" "src/*.cu")
file(GLOB_RECURSE HEADERS "include/*.h" "include/*.hpp")

# 添加可执行文件（必须先定义目标）
add_executable(${PROJECT_NAME}
    ${SOURCES}
    ${HEADERS}  # 可选，仅用于 IDE 显示
)

# 设置包含目录（必须在 add_executable 之后）
target_include_directories(${PROJECT_NAME} PRIVATE
    ${PROJECT_SOURCE_DIR}/include
    ${OpenCV_INCLUDE_DIRS}
    ${CUDA_INCLUDE_DIRS}
    ${TENSORRT_INCLUDE_DIR}
)

# 链接库
target_link_libraries(${PROJECT_NAME} PRIVATE
    ${OpenCV_LIBS}
    ${CUDA_LIBRARIES}
    ${TENSORRT_LIB}
    ${NVINFER_PLUGIN_LIB}
)