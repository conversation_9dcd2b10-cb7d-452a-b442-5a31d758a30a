#include <iostream>
#include <fstream>
#include <vector>
#include <cmath>
#include <complex>
#include <algorithm>
#include <stdexcept>
#include <fftw3.h>
#include <cstring>

// 帧头结构定义
struct FrameHeader {
    uint32_t marker;
    uint16_t channel;
    uint16_t circle_num;
    uint16_t frame_num;
    uint8_t  task_type;
    uint8_t  group_id;
    uint16_t group_limit;
    uint16_t pulse_id;
    uint8_t  waveform;
    uint8_t  waveform_type;
    uint16_t frame_length;
    uint32_t angle; // 方位角*100
    uint16_t angle_counter;
    char     reserved[14];
};

const int ROWS = 1024;
const int COLS = 2048;
const float X_START = -3.6f;
const float X_END = 3.6f;
const float X_STEP = 0.1f;
const float offsets[5] = {2.5f, 7.5f, 12.5f, 20.0f, 31.25f};
const float range_limits[5][2] = {{0, 5}, {5, 10}, {10, 15}, {15, 25}, {25, 37.5}};
std::vector<float> x_axis;
std::vector<std::vector<float>> hecha_table;

void loadHechaTable(const std::string& path) {
    std::ifstream file(path);
    if (!file.is_open()) throw std::runtime_error("无法打开hecha表文件");

    hecha_table.clear();
    std::string line;
    while (std::getline(file, line)) {
        std::vector<float> row(5);
        sscanf(line.c_str(), "%f,%f,%f,%f,%f", &row[0], &row[1], &row[2], &row[3], &row[4]);
        // for (auto& val : row) val *= -1;
        hecha_table.push_back(row);
    }
    for (float x = X_START; x <= X_END + 1e-6; x += X_STEP)
        x_axis.push_back(x);
}

int getLineIndex(int row_idx) {
    if (row_idx >= 1 && row_idx <= 512) return 0;
    if (row_idx > 512 && row_idx <= 768) return 1;
    if (row_idx > 768 && row_idx <= 896) return 2;
    if (row_idx > 896 && row_idx <= 960) return 3;
    if (row_idx > 960 && row_idx <= 1024) return 4;
    throw std::runtime_error("无效的行索引");
}

float findXWithOffset(int line_idx, float y_target) {
    float min_diff = 1e9;
    int best_idx = 0;
    for (size_t i = 0; i < hecha_table.size(); ++i) {
        float diff = std::abs(hecha_table[i][line_idx] - y_target);
        if (diff < min_diff) {
            min_diff = diff;
            best_idx = i;
        }
    }
    float x_val = x_axis[best_idx] + offsets[line_idx];
    return std::min(std::max(x_val, range_limits[line_idx][0]), range_limits[line_idx][1]);
}

std::vector<std::complex<float>> applyHanning(const std::vector<std::complex<float>>& row) {
    std::vector<std::complex<float>> windowed(row.size());
    for (size_t i = 0; i < row.size(); ++i) {
        float w = 0.5f * (1.0f - std::cos(2 * M_PI * i / (row.size() - 1)));
        windowed[i] = row[i] * w;
    }
    return windowed;
}

void performFFT2D(const std::vector<std::vector<std::complex<float>>>& data,
                  std::vector<std::vector<std::complex<float>>>& fft_result) {
    fft_result.resize(ROWS, std::vector<std::complex<float>>(COLS));
    fftwf_complex *in = (fftwf_complex*) fftwf_malloc(sizeof(fftwf_complex) * COLS);
    fftwf_complex *out = (fftwf_complex*) fftwf_malloc(sizeof(fftwf_complex) * COLS);
    fftwf_plan p = fftwf_plan_dft_1d(COLS, in, out, FFTW_FORWARD, FFTW_ESTIMATE);

    for (int row = 0; row < ROWS; ++row) {
        for (int i = 0; i < COLS; ++i) {
            in[i][0] = data[row][i].real();
            in[i][1] = data[row][i].imag();
        }
        fftwf_execute(p);
        for (int i = 0; i < COLS; ++i)
            fft_result[row][i] = {out[i][0], out[i][1]};
    }
    fftwf_destroy_plan(p);
    fftwf_free(in);
    fftwf_free(out);
}

void readDualFrameBin(const std::string& path,
                      FrameHeader& S_head, FrameHeader& D_head,
                      std::vector<std::vector<std::complex<float>>>& S_data,
                      std::vector<std::vector<std::complex<float>>>& D_data) {
    const size_t FRAME_HEADER_SIZE = 40;
    const size_t FRAME_DATA_SIZE = ROWS * COLS * 2 * sizeof(float); // 复数：实虚交错

    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) throw std::runtime_error("无法打开bin文件");

    char header_buf[FRAME_HEADER_SIZE];
    file.read(header_buf, FRAME_HEADER_SIZE);
    std::memcpy(&S_head, header_buf, sizeof(FrameHeader));

    std::vector<float> buf1(ROWS * COLS * 2);
    file.read(reinterpret_cast<char*>(buf1.data()), FRAME_DATA_SIZE);
    S_data.resize(ROWS, std::vector<std::complex<float>>(COLS));
    for (int i = 0; i < ROWS; ++i)
        for (int j = 0; j < COLS; ++j)
            S_data[i][j] = std::complex<float>(buf1[(i * COLS + j) * 2], buf1[(i * COLS + j) * 2 + 1]);

    file.read(header_buf, FRAME_HEADER_SIZE);
    std::memcpy(&D_head, header_buf, sizeof(FrameHeader));

    std::vector<float> buf2(ROWS * COLS * 2);
    file.read(reinterpret_cast<char*>(buf2.data()), FRAME_DATA_SIZE);
    D_data.resize(ROWS, std::vector<std::complex<float>>(COLS));
    for (int i = 0; i < ROWS; ++i)
        for (int j = 0; j < COLS; ++j)
            D_data[i][j] = std::complex<float>(buf2[(i * COLS + j) * 2], buf2[(i * COLS + j) * 2 + 1]);
}

std::vector<std::pair<float, float>> computeElevationAngles(const FrameHeader& S_head,
    const std::vector<std::vector<std::complex<float>>>& S_data,
    const std::vector<std::vector<std::complex<float>>>& D_data,
    const std::vector<std::pair<int, int>>& test_points) {

    std::vector<std::vector<std::complex<float>>> S_fft, D_fft;
    std::vector<std::vector<std::complex<float>>> S_windowed = S_data;
    std::vector<std::vector<std::complex<float>>> D_windowed = D_data;

    for (int i = 0; i < ROWS; ++i) {
        S_windowed[i] = applyHanning(S_data[i]);
        D_windowed[i] = applyHanning(D_data[i]);
    }

    performFFT2D(S_windowed, S_fft);
    performFFT2D(D_windowed, D_fft);

    std::vector<std::pair<float, float>> result;
    for (const auto& [row, col] : test_points) {
        auto val_s = S_fft[row - 1][col];
        auto val_d = D_fft[row - 1][col];
        float amp_s = std::abs(val_s);
        float amp_d = std::abs(val_d);
        float amp_ratio = amp_d / (amp_s + 1e-6);
        float phase_diff = std::signbit(std::cos(std::arg(val_d) - std::arg(val_s))) ? -1.f : 1.f;
        int line_idx = getLineIndex(row);
        float angle = findXWithOffset(line_idx, phase_diff * amp_ratio);
        float azimuth_deg = S_head.angle / 100.0f;
        result.emplace_back(angle, azimuth_deg);
    }
    return result;
}

int main() {
    loadHechaTable("data/hecha_table.csv");

    FrameHeader S_head, D_head;
    std::vector<std::vector<std::complex<float>>> S_data, D_data;
    readDualFrameBin("data/3_33294_28506_SHSDDHDD.bin", S_head, D_head, S_data, D_data);

    std::vector<std::pair<int, int>> test_points = {
        {300, 1000}, {600, 1000}, {800, 1000}, {900, 1000}, {1000, 1000}
    };

    auto results = computeElevationAngles(S_head, S_data, D_data, test_points);
    for (const auto& [elev, azim] : results) {
        std::cout << "俯仰角: " << elev << "°, 方位角: " << azim << "°\n";
    }

    return 0;
}
