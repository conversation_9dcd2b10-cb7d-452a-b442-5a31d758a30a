{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/**", "/usr/include", "/usr/local/include/opencv4", "/home/<USER>/My_APP/cnpy/cnpy", "/usr/local/cuda/include", "/home/<USER>/My_APP/TensorRT-8.6.1.6/include", "/home/<USER>/My_APP/TensorRT-8.6.1.6/samples/common"], "defines": [], "compilerPath": "/usr/bin/gcc", "cStandard": "c17", "cppStandard": "gnu++17", "intelliSenseMode": "linux-gcc-x64"}], "version": 4}