{"version": "0.2.0", "configurations": [{"name": "GDB Launch MSHNet_TensorRT_Test", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/MSHNet_TensorRT_Test", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "CMake Build"}]}