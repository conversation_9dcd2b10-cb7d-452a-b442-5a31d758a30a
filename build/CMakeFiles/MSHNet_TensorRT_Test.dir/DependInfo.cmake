
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/My_Project/MSHNet_TensorRT_Test/src/infer_engine.cpp" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o" "gcc" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o.d"
  "/home/<USER>/My_Project/MSHNet_TensorRT_Test/src/main.cpp" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o" "gcc" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o.d"
  "/home/<USER>/My_Project/MSHNet_TensorRT_Test/src/postprocess.cpp" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o" "gcc" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o.d"
  "/home/<USER>/My_Project/MSHNet_TensorRT_Test/src/preprocess.cpp" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o" "gcc" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o.d"
  "/home/<USER>/My_Project/MSHNet_TensorRT_Test/src/utils.cpp" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o" "gcc" "CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
