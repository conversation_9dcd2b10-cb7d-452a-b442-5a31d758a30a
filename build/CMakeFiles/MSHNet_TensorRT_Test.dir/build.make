# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test/build

# Include any dependencies generated for this target.
include CMakeFiles/MSHNet_TensorRT_Test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/MSHNet_TensorRT_Test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/MSHNet_TensorRT_Test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MSHNet_TensorRT_Test.dir/flags.make

CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o: ../src/infer_engine.cpp
CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/infer_engine.cpp

CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/infer_engine.cpp > CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.i

CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/infer_engine.cpp -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.s

CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o: ../src/main.cpp
CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/main.cpp

CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/main.cpp > CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.i

CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/main.cpp -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.s

CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o: ../src/postprocess.cpp
CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/postprocess.cpp

CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/postprocess.cpp > CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.i

CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/postprocess.cpp -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.s

CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o: ../src/preprocess.cpp
CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/preprocess.cpp

CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/preprocess.cpp > CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.i

CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/preprocess.cpp -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.s

CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o: ../src/utils.cpp
CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o: CMakeFiles/MSHNet_TensorRT_Test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/utils.cpp

CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/utils.cpp > CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.i

CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/utils.cpp -o CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.s

# Object files for target MSHNet_TensorRT_Test
MSHNet_TensorRT_Test_OBJECTS = \
"CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o" \
"CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o" \
"CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o" \
"CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o" \
"CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o"

# External object files for target MSHNet_TensorRT_Test
MSHNet_TensorRT_Test_EXTERNAL_OBJECTS =

MSHNet_TensorRT_Test: CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o
MSHNet_TensorRT_Test: CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o
MSHNet_TensorRT_Test: CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o
MSHNet_TensorRT_Test: CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o
MSHNet_TensorRT_Test: CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o
MSHNet_TensorRT_Test: CMakeFiles/MSHNet_TensorRT_Test.dir/build.make
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_gapi.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_highgui.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_ml.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_objdetect.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_photo.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_stitching.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_video.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_videoio.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/cuda/lib64/libcudart_static.a
MSHNet_TensorRT_Test: /usr/lib/x86_64-linux-gnu/librt.a
MSHNet_TensorRT_Test: /home/<USER>/My_APP/TensorRT-8.6.1.6/lib/libnvinfer.so
MSHNet_TensorRT_Test: /home/<USER>/My_APP/TensorRT-8.6.1.6/lib/libnvinfer_plugin.so
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_imgcodecs.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_dnn.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_calib3d.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_features2d.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_flann.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_imgproc.so.4.8.1
MSHNet_TensorRT_Test: /usr/local/lib/libopencv_core.so.4.8.1
MSHNet_TensorRT_Test: CMakeFiles/MSHNet_TensorRT_Test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX executable MSHNet_TensorRT_Test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/MSHNet_TensorRT_Test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/MSHNet_TensorRT_Test.dir/build: MSHNet_TensorRT_Test
.PHONY : CMakeFiles/MSHNet_TensorRT_Test.dir/build

CMakeFiles/MSHNet_TensorRT_Test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MSHNet_TensorRT_Test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MSHNet_TensorRT_Test.dir/clean

CMakeFiles/MSHNet_TensorRT_Test.dir/depend:
	cd /home/<USER>/My_Project/MSHNet_TensorRT_Test/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/My_Project/MSHNet_TensorRT_Test /home/<USER>/My_Project/MSHNet_TensorRT_Test /home/<USER>/My_Project/MSHNet_TensorRT_Test/build /home/<USER>/My_Project/MSHNet_TensorRT_Test/build /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles/MSHNet_TensorRT_Test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/MSHNet_TensorRT_Test.dir/depend

