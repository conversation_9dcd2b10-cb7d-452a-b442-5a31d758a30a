# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles /home/<USER>/My_Project/MSHNet_TensorRT_Test/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named MSHNet_TensorRT_Test

# Build rule for target.
MSHNet_TensorRT_Test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 MSHNet_TensorRT_Test
.PHONY : MSHNet_TensorRT_Test

# fast build rule for target.
MSHNet_TensorRT_Test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/build
.PHONY : MSHNet_TensorRT_Test/fast

src/infer_engine.o: src/infer_engine.cpp.o
.PHONY : src/infer_engine.o

# target to build an object file
src/infer_engine.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.o
.PHONY : src/infer_engine.cpp.o

src/infer_engine.i: src/infer_engine.cpp.i
.PHONY : src/infer_engine.i

# target to preprocess a source file
src/infer_engine.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.i
.PHONY : src/infer_engine.cpp.i

src/infer_engine.s: src/infer_engine.cpp.s
.PHONY : src/infer_engine.s

# target to generate assembly for a file
src/infer_engine.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/infer_engine.cpp.s
.PHONY : src/infer_engine.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/postprocess.o: src/postprocess.cpp.o
.PHONY : src/postprocess.o

# target to build an object file
src/postprocess.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.o
.PHONY : src/postprocess.cpp.o

src/postprocess.i: src/postprocess.cpp.i
.PHONY : src/postprocess.i

# target to preprocess a source file
src/postprocess.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.i
.PHONY : src/postprocess.cpp.i

src/postprocess.s: src/postprocess.cpp.s
.PHONY : src/postprocess.s

# target to generate assembly for a file
src/postprocess.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/postprocess.cpp.s
.PHONY : src/postprocess.cpp.s

src/preprocess.o: src/preprocess.cpp.o
.PHONY : src/preprocess.o

# target to build an object file
src/preprocess.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.o
.PHONY : src/preprocess.cpp.o

src/preprocess.i: src/preprocess.cpp.i
.PHONY : src/preprocess.i

# target to preprocess a source file
src/preprocess.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.i
.PHONY : src/preprocess.cpp.i

src/preprocess.s: src/preprocess.cpp.s
.PHONY : src/preprocess.s

# target to generate assembly for a file
src/preprocess.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/preprocess.cpp.s
.PHONY : src/preprocess.cpp.s

src/utils.o: src/utils.cpp.o
.PHONY : src/utils.o

# target to build an object file
src/utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.o
.PHONY : src/utils.cpp.o

src/utils.i: src/utils.cpp.i
.PHONY : src/utils.i

# target to preprocess a source file
src/utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.i
.PHONY : src/utils.cpp.i

src/utils.s: src/utils.cpp.s
.PHONY : src/utils.s

# target to generate assembly for a file
src/utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/src/utils.cpp.s
.PHONY : src/utils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... MSHNet_TensorRT_Test"
	@echo "... src/infer_engine.o"
	@echo "... src/infer_engine.i"
	@echo "... src/infer_engine.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/postprocess.o"
	@echo "... src/postprocess.i"
	@echo "... src/postprocess.s"
	@echo "... src/preprocess.o"
	@echo "... src/preprocess.i"
	@echo "... src/preprocess.s"
	@echo "... src/utils.o"
	@echo "... src/utils.i"
	@echo "... src/utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

