#include "utils.hpp"
#include <dirent.h>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <NvInferPlugin.h>

int extractNumber(const std::string& str) {
    std::string numberStr;
    for (char c : str) if (isdigit(c)) numberStr += c;
    return numberStr.empty() ? 0 : std::stoi(numberStr);
}

bool compareFilesByNumber(const std::string& a, const std::string& b) {
    return extractNumber(a) < extractNumber(b);
}

std::vector<std::string> get_bin_files(const std::string& folder_path) {
    std::vector<std::string> bin_files;
    DIR* dir = opendir(folder_path.c_str());
    if (!dir) {
        std::cerr << "Cannot open directory: " << folder_path << std::endl;
        return bin_files;
    }
    dirent* ent;
    while ((ent = readdir(dir))) {
        std::string name(ent->d_name);
        if (name.size() > 4 && name.substr(name.size() - 4) == ".bin")
            bin_files.push_back(folder_path + "/" + name);
    }
    closedir(dir);
    std::sort(bin_files.begin(), bin_files.end(), compareFilesByNumber);
    return bin_files;
}

std::string to_string_6digits(int i) {
    std::ostringstream oss;
    oss << std::setw(6) << std::setfill('0') << i;
    return oss.str();
}

void initializeCustomPlugins() {
    static bool initialized = false;
    if (!initialized) {
        initLibNvInferPlugins(nullptr, "");
        initialized = true;
    }
}
