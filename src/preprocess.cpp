#include "preprocess.hpp"
#include <thread>
#include <algorithm>

void sample_data(const std::vector<double>& input, std::vector<double>& output, size_t sample_size) {
    const size_t stride = sample_size * 2;
    const size_t n = input.size() / stride * sample_size;
    output.resize(n);
    const double* input_data = input.data();
    double* output_data = output.data();

    for (size_t i = 0; i < n; ++i) {
        output_data[i] = input_data[(i / sample_size) * stride + (i % sample_size)];
    }
}

void normalize_chunk(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t start,
    size_t end,
    double real_offset,
    double real_scale_inv,
    double imag_offset,
    double imag_scale_inv
) {
    for (size_t i = start; i < end; i += 2) {
        output_tensor[i]     = static_cast<float>((input_tensor[i] - real_offset) * real_scale_inv);
        output_tensor[i + 1] = static_cast<float>((input_tensor[i + 1] - imag_offset) * imag_scale_inv);
    }
}

void normalize_input_tensor_multithreaded(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t num_threads
) {
    const double real_offset = 449189.038163;
    const double real_scale_inv = 1.0 / 516448646.330213;
    const double imag_offset = -165938.973867;
    const double imag_scale_inv = 1.0 / 519231985.788598;

    const size_t num_elements = input_tensor.size();
    output_tensor.resize(num_elements);
    const size_t chunk_size = (num_elements + num_threads - 1) / num_threads;

    std::vector<std::thread> threads;
    for (size_t i = 0; i < num_threads; ++i) {
        size_t start = i * chunk_size;
        size_t end = std::min(start + chunk_size, num_elements);
        if (start % 2 != 0) --start;

        threads.emplace_back(normalize_chunk,
            std::ref(input_tensor), std::ref(output_tensor),
            start, end, real_offset, real_scale_inv, imag_offset, imag_scale_inv
        );
    }

    for (auto& thread : threads) thread.join();
}

void sample_and_normalize(
    const std::vector<double>& input,
    std::vector<float>& output,
    size_t sample_size,
    size_t num_threads
) {
    const size_t stride = sample_size * 2;
    const size_t total_blocks = input.size() / stride;
    const size_t n = total_blocks * sample_size;
    output.resize(n);

    const double real_offset = 449189.038163;
    const double real_scale_inv = 1.0 / 516448646.330213;
    const double imag_offset = -165938.973867;
    const double imag_scale_inv = 1.0 / 519231985.788598;

    const size_t blocks_per_thread = (total_blocks + num_threads - 1) / num_threads;
    std::vector<std::thread> threads;

    for (size_t t = 0; t < num_threads; ++t) {
        size_t start_block = t * blocks_per_thread;
        size_t end_block = std::min(start_block + blocks_per_thread, total_blocks);
        threads.emplace_back([&, start_block, end_block]() {
            for (size_t block = start_block; block < end_block; ++block) {
                size_t base_input_idx = block * stride;
                size_t base_output_idx = block * sample_size;
                for (size_t offset = 0; offset < sample_size; ++offset) {
                    size_t input_idx = base_input_idx + offset;
                    if (input_idx >= input.size()) break;
                    output[base_output_idx + offset] = static_cast<float>(
                        ((input_idx % 2 == 0) ?
                         (input[input_idx] - real_offset) * real_scale_inv :
                         (input[input_idx] - imag_offset) * imag_scale_inv)
                    );
                }
            }
        });
    }

    for (auto& thread : threads) thread.join();
}
