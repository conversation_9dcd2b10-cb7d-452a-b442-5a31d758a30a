#include "preprocess.hpp"
#include "postprocess.hpp"
#include "infer_engine.hpp"
#include "utils.hpp"

#include <cuda_runtime_api.h>
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>
#include <fstream>
#include <iomanip>

int main() {
    std::string engine_path, test_path, output_path;
    std::cout << "请输入 TensorRT 模型的路径: ";
    std::getline(std::cin, engine_path);
    std::cout << "请输入待测数据文件夹的路径: ";
    std::getline(std::cin, test_path);
    std::cout << "请输入输出图像保存文件夹的路径: ";
    std::getline(std::cin, output_path);

    if (engine_path.empty() || test_path.empty() || output_path.empty()) {
        std::cerr << "使用默认路径" << std::endl;
        engine_path = "/home/<USER>/My_Project/MSHNet/trt/DP-fp16.trt";
        test_path = "/home/<USER>/My_Project/trt_test/bin";
        output_path = "/home/<USER>/My_Project/trt_test/real_vision";
    }

    initializeCustomPlugins();

    nvinfer1::IRuntime* runtime = nullptr;
    nvinfer1::ICudaEngine* engine = loadEngine(engine_path, runtime);
    nvinfer1::IExecutionContext* context = engine->createExecutionContext();

    cudaStream_t stream;
    cudaStreamCreate(&stream);

    const int inputIndex = 0, outputIndex = 1;
    const auto input_dims = engine->getBindingDimensions(inputIndex);
    const auto output_dims = engine->getBindingDimensions(outputIndex);

    const int input_h = input_dims.d[2];
    const int input_w = input_dims.d[3];
    const size_t input_size = input_dims.d[1] * input_h * input_w;
    const size_t output_size = output_dims.d[1] * output_dims.d[2];

    std::cout << "Input shape: [" << input_dims.d[1] << "x" << input_dims.d[2] << "x" << input_dims.d[3] << "]" << std::endl;

    void* buffers[2];
    cudaMalloc(&buffers[inputIndex], input_size * sizeof(float));
    cudaMalloc(&buffers[outputIndex], output_size * sizeof(float));

    std::vector<float> input_tensor(input_size);
    std::vector<float> output_prob(output_size);
    std::vector<double> raw_input(1024 * 2048 * 2); // 原始输入
    auto bin_files = get_bin_files(test_path);

    for (const auto& file : bin_files) {
        std::ifstream fin(file, std::ios::binary);
        if (!fin) {
            std::cerr << "Failed to open " << file << std::endl;
            continue;
        }
        fin.read(reinterpret_cast<char*>(raw_input.data()), raw_input.size() * sizeof(double));
        fin.close();

        auto t0 = std::chrono::high_resolution_clock::now();
        sample_and_normalize(raw_input, input_tensor, 4096, 4);
        auto t1 = std::chrono::high_resolution_clock::now();

        cudaMemcpyAsync(buffers[inputIndex], input_tensor.data(), input_tensor.size() * sizeof(float), cudaMemcpyHostToDevice, stream);
        cudaStreamSynchronize(stream);
        auto t2 = std::chrono::high_resolution_clock::now();

        context->enqueueV2(buffers, stream, nullptr);
        cudaStreamSynchronize(stream);
        auto t3 = std::chrono::high_resolution_clock::now();

        cudaMemcpyAsync(output_prob.data(), buffers[outputIndex], output_prob.size() * sizeof(float), cudaMemcpyDeviceToHost, stream);
        cudaStreamSynchronize(stream);
        auto t4 = std::chrono::high_resolution_clock::now();

        auto centers = post_process(output_prob.data(), 512, 1024);
        auto t5 = std::chrono::high_resolution_clock::now();

        std::cout << "目标坐标: ";
        for (const auto& pt : centers)
            std::cout << std::fixed << std::setprecision(2) << "(" << pt.first * 2 << ", " << 512 - pt.second * 2 << ") ";
        std::cout << std::endl;

        int t_pre = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
        int t_gpuin = std::chrono::duration_cast<std::chrono::milliseconds>(t2 - t1).count();
        int t_inf = std::chrono::duration_cast<std::chrono::microseconds>(t3 - t2).count();
        int t_gpuout = std::chrono::duration_cast<std::chrono::milliseconds>(t4 - t3).count();
        int t_post = std::chrono::duration_cast<std::chrono::milliseconds>(t5 - t4).count();
        int total = t_pre + t_gpuin + t_gpuout + t_post + t_inf / 1000;

        std::cout << "预处理: " << t_pre << "ms, 上传: " << t_gpuin << "ms, 推理: " << t_inf << "us, 下载: " << t_gpuout << "ms, 后处理: " << t_post << "ms\n";
        std::cout << "总耗时: " << total << "ms, 帧率: " << std::fixed << std::setprecision(2) << 1000.0 / total << " fps\n";

        // 保存图像
        cv::Mat press(512, 1024, CV_8U);
        binarize(output_prob.data(), press, 512, 1024);
        std::string fname = file.substr(file.find_last_of("/\\") + 1);
        fname = fname.substr(0, fname.find(".bin")) + "_Pred.png";
        std::string out_file = output_path + "/" + fname;
        std::cout << "保存图像: " << out_file << std::endl;
        cv::imwrite(out_file, press);
    }

    // 清理资源
    cudaFree(buffers[inputIndex]);
    cudaFree(buffers[outputIndex]);
    context->destroy();
    engine->destroy();
    runtime->destroy();
    cudaStreamDestroy(stream);

    return 0;
}
