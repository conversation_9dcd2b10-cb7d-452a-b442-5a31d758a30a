#include <iostream>
#include <fstream>
#include <filesystem>
#include <vector>
#include <deque>
#include <sstream>
#include <cstring>
#include <mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <algorithm>
#include <chrono>

namespace fs = std::filesystem;

// 常量定义
const uint32_t FRAME_HEADER_MARKER = 0xAAAA5555;  // 帧头标识
const size_t HEADER_SIZE = 40;                    // 帧头大小
const size_t SAMPLE_DATA_SIZE = 2 * 2 * 4096;     // 2通道 × 每通道2字节 × 4096点
const size_t MAX_PULSE_BUFFER_SIZE = 1500;        // 脉冲缓冲区最大大小
const int MAX_THREADS = 4;                        // 最大线程数限制

// 数据保存选项
struct SaveOptions {
    bool save_sum_header = true;    // 保存和通道帧头
    bool save_sum_data = true;      // 保存和通道重排数据
    bool save_diff_header = true;   // 保存差通道帧头
    bool save_diff_data = true;     // 保存差通道重排数据
};

// 配置选项
struct ProcessingOptions {
    bool save_csv = true;          // 是否保存CSV帧头信息
    bool verbose_log = false;      // 详细日志输出
    size_t io_buffer_size = 8192;  // I/O缓冲区大小(字节)
    SaveOptions save_opts;         // 数据保存选项
};

#pragma pack(push, 1)
// 帧头结构体
struct FrameHeader {
    uint32_t marker;
    uint16_t channel;
    uint16_t circle_num;
    uint16_t frame_num;
    uint8_t  task_type;
    uint8_t  group_id;
    uint16_t group_limit;
    uint16_t pulse_id;
    uint8_t  waveform;
    uint8_t  waveform_type;
    uint16_t frame_length;
    uint32_t angle;
    uint16_t angle_counter;
    char     reserved[14];
};
#pragma pack(pop)

// 脉冲数据结构
struct Pulse {
    FrameHeader header;           // 原始帧头
    std::vector<char> samples;    // 原始样本
    Pulse(FrameHeader&& h, std::vector<char>&& s)
        : header(std::move(h)), samples(std::move(s)) {}
};

// 写入CSV文件表头
void write_csv_header(std::ostream& out) {
    out << "通道,圈号,帧号,脉冲编号,方向角,方向角更新计数器\n";
}

// 追加CSV数据行
void append_csv_row(std::ostringstream& out, const FrameHeader& h) {
    out << h.channel << ","
        << h.circle_num << ","
        << h.frame_num << ","
        << h.pulse_id << ","
        << h.angle << ","
        << h.angle_counter << "\n";
}

/**
 * 对单通道1024个脉冲数据进行重排并返回float数组
 */
std::vector<float> rearrange_channel(const std::vector<Pulse>& group) {
    // 生成重排索引
    std::vector<int> indices;
    indices.reserve(1024);
    for (int i = 0; i < 1024; i += 2)    indices.push_back(i);
    for (int i = 1; i < 1024; i += 4)    indices.push_back(i);
    for (int i = 3; i < 1024; i += 8)    indices.push_back(i);
    for (int i = 7; i < 1024; i += 16)   indices.push_back(i);
    for (int i = 15; i < 1024; i += 16)  indices.push_back(i);

    size_t float_count_per_pulse = SAMPLE_DATA_SIZE / sizeof(float);
    std::vector<float> data;
    data.reserve(1024 * float_count_per_pulse);

    // 按索引重排并汇聚到同一buffer
    for (int idx : indices) {
        const float* fp = reinterpret_cast<const float*>(group[idx].samples.data());
        data.insert(data.end(), fp, fp + float_count_per_pulse);
    }
    return data;
}

/**
 * 合并并保存和、差通道的重排结果
 * 保存格式可选：和通道头 + 和通道重排数据 + 差通道头 + 差通道重排数据
 */
bool save_dual_channel(const FrameHeader& h_sum,
                       const std::vector<float>& data_sum,
                       const FrameHeader& h_diff,
                       const std::vector<float>& data_diff,
                       const std::string& out_dir,
                       const ProcessingOptions& options)
{
    try {
        // 创建目录
        fs::create_directories(out_dir);

        // 生成文件名（使用和通道的信息作为基准）
        std::string fname = std::to_string(h_sum.circle_num)
                          + "_" + std::to_string(h_sum.frame_num)
                          + "_" + std::to_string(h_sum.angle);

        // 根据保存选项添加后缀
        std::string suffix = "";
        if (options.save_opts.save_sum_header) suffix += "SH";
        if (options.save_opts.save_sum_data) suffix += "SD";
        if (options.save_opts.save_diff_header) suffix += "DH";
        if (options.save_opts.save_diff_data) suffix += "DD";
        if (!suffix.empty()) fname += "_" + suffix;
        fname += ".bin";

        std::string out_path = out_dir + "/" + fname;

        std::ofstream out(out_path, std::ios::binary | std::ios::trunc);
        if (!out) return false;

        // 缓冲区
        char* buf = new char[options.io_buffer_size];
        out.rdbuf()->pubsetbuf(buf, options.io_buffer_size);

        // 按选项写入数据
        if (options.save_opts.save_sum_header) {
            out.write(reinterpret_cast<const char*>(&h_sum), HEADER_SIZE);
        }

        if (options.save_opts.save_sum_data) {
            out.write(reinterpret_cast<const char*>(data_sum.data()),
                      data_sum.size() * sizeof(float));
        }

        if (options.save_opts.save_diff_header) {
            out.write(reinterpret_cast<const char*>(&h_diff), HEADER_SIZE);
        }

        if (options.save_opts.save_diff_data) {
            out.write(reinterpret_cast<const char*>(data_diff.data()),
                      data_diff.size() * sizeof(float));
        }

        out.close();
        delete[] buf;

        if (options.verbose_log) {
            std::cout << "已保存文件: " << out_path
                      << " (SH:" << options.save_opts.save_sum_header
                      << " SD:" << options.save_opts.save_sum_data
                      << " DH:" << options.save_opts.save_diff_header
                      << " DD:" << options.save_opts.save_diff_data << ")" << std::endl;
        }
        return true;
    } catch (...) {
        return false;
    }
}

/**
 * 处理单个 .bin 文件，基于数据格式：和通道帧头+和通道数据+差通道帧头+差通道数据
 * 优化的双通道匹配逻辑：利用相邻的和差通道数据必然具有相同脉冲ID的特性
 */
bool process_single_bin_file(const std::string& file_path,
                             int azimuth_min,
                             int azimuth_max,
                             const std::string& output_base,
                             const ProcessingOptions& options)
{
    auto t0 = std::chrono::steady_clock::now();
    if (options.verbose_log) {
        std::cout << "[开始] " << fs::path(file_path).filename() << std::endl;
    }

    std::ifstream in(file_path, std::ios::binary);
    if (!in) return false;
    char* in_buf = new char[options.io_buffer_size];
    in.rdbuf()->pubsetbuf(in_buf, options.io_buffer_size);

    // 输出目录 & CSV
    std::string stem = fs::path(file_path).stem().string();
    std::string dir_out = output_base + "/" + stem;
    fs::create_directories(dir_out);
    std::string bin_dir = dir_out + "/双通道重排";
    std::string csv_path = dir_out + "/frames.csv";

    std::ofstream csv_out;
    std::ostringstream csv_buf;
    if (options.save_csv) {
        csv_out.open(csv_path);
        write_csv_header(csv_buf);
    }

    // 优化的双通道处理：利用数据格式特性
    std::deque<Pulse> buf_sum, buf_diff;  // 和通道、差通道缓冲区
    std::vector<char> samp_buf(SAMPLE_DATA_SIZE);
    FrameHeader header;
    size_t saved = 0;
    size_t total_sum = 0, total_diff = 0;  // 统计各通道脉冲数

    // 期待下一个帧的类型：1=和通道, 16=差通道
    uint16_t expected_channel = 1;  // 开始期待和通道

    while (in.read(reinterpret_cast<char*>(&header), HEADER_SIZE)) {
        if (header.marker != FRAME_HEADER_MARKER) {
            in.seekg(-static_cast<int>(HEADER_SIZE) + 1, std::ios::cur);
            continue;
        }
        if (!in.read(samp_buf.data(), SAMPLE_DATA_SIZE)) break;

        // CSV 记录
        if (options.save_csv) {
            append_csv_row(csv_buf, header);
        }

        // 方位角过滤
        if (header.angle < azimuth_min || header.angle > azimuth_max) {
            continue;
        }

        // 基于数据格式的优化处理逻辑
        if (header.channel == 1) {  // 和通道
            total_sum++;
            auto pulse = Pulse(std::move(header), std::move(samp_buf));
            samp_buf.assign(SAMPLE_DATA_SIZE, 0);
            buf_sum.push_back(std::move(pulse));
            if (buf_sum.size() > MAX_PULSE_BUFFER_SIZE) buf_sum.pop_front();
            expected_channel = 16;  // 下一个应该是差通道

        } else if (header.channel == 16) {  // 差通道
            total_diff++;
            auto pulse = Pulse(std::move(header), std::move(samp_buf));
            samp_buf.assign(SAMPLE_DATA_SIZE, 0);
            buf_diff.push_back(std::move(pulse));
            if (buf_diff.size() > MAX_PULSE_BUFFER_SIZE) buf_diff.pop_front();
            expected_channel = 1;   // 下一个应该是和通道
        }

        // 定期输出缓冲区状态
        if (options.verbose_log && (total_sum + total_diff) % 10000 == 0) {
            std::cout << "缓冲区状态 - 和通道: " << buf_sum.size()
                      << ", 差通道: " << buf_diff.size()
                      << ", 已保存组数: " << saved << std::endl;
        }

        // 优化的脉冲组处理：利用数据格式的连续性
        // 当两个缓冲区都有足够数据时，尝试匹配和保存
        if (buf_sum.size() >= 1024 && buf_diff.size() >= 1024) {
            // 查找和通道中的脉冲组起始位置（pulse_id==3）
            auto sum_it = std::find_if(buf_sum.rbegin(), buf_sum.rend(),
                [](const auto& p){ return p.header.pulse_id == 3; });

            if (sum_it != buf_sum.rend()) {
                size_t sum_start = buf_sum.rend() - sum_it - 1;
                if (sum_start + 1024 <= buf_sum.size()) {
                    // 基于数据格式特性：查找对应的差通道脉冲组
                    // 由于数据格式是连续的，差通道应该紧跟在和通道之后
                    auto& ref_sum = buf_sum[sum_start];

                    // 在差通道中查找匹配的脉冲组
                    size_t diff_start = 0;
                    bool found_match = false;

                    for (size_t i = 0; i <= buf_diff.size() - 1024; i++) {
                        if (buf_diff[i].header.pulse_id == 3) {
                            auto& ref_diff = buf_diff[i];

                            // 检查是否为同一脉冲组（相同的圈号、帧号、角度）
                            if (ref_sum.header.circle_num == ref_diff.header.circle_num &&
                                ref_sum.header.frame_num == ref_diff.header.frame_num &&
                                abs(static_cast<int>(ref_sum.header.angle) -
                                   static_cast<int>(ref_diff.header.angle)) <= 100) {
                                diff_start = i;
                                found_match = true;
                                break;
                            }
                        }
                    }

                    if (found_match) {
                        // 提取1024脉冲组
                        std::vector<Pulse> grp_sum(buf_sum.begin() + sum_start,
                                                  buf_sum.begin() + sum_start + 1024);
                        std::vector<Pulse> grp_diff(buf_diff.begin() + diff_start,
                                                   buf_diff.begin() + diff_start + 1024);

                        // 重排数据
                        auto data_sum = rearrange_channel(grp_sum);
                        auto data_diff = rearrange_channel(grp_diff);

                        // 保存双通道数据
                        if (save_dual_channel(
                            grp_sum.front().header, data_sum,
                            grp_diff.front().header, data_diff,
                            bin_dir, options)) {
                            saved++;
                            if (options.verbose_log) {
                                std::cout << "保存双通道组: 圈号=" << grp_sum.front().header.circle_num
                                          << ", 帧号=" << grp_sum.front().header.frame_num
                                          << ", 角度=" << grp_sum.front().header.angle << std::endl;
                            }
                        }

                        // 清理已处理的数据
                        buf_sum.erase(buf_sum.begin(), buf_sum.begin() + sum_start + 512);
                        buf_diff.erase(buf_diff.begin(), buf_diff.begin() + diff_start + 512);
                    }
                }
            }
        }
    }

    if (options.save_csv) {
        csv_out << csv_buf.str();
        csv_out.close();
    }
    in.close();
    delete[] in_buf;

    if (options.verbose_log) {
        auto t1 = std::chrono::steady_clock::now();
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
        std::cout << "[完成] " << fs::path(file_path).filename()
                  << "，和通道脉冲: " << total_sum
                  << "，差通道脉冲: " << total_diff
                  << "，保存组数: " << saved
                  << "，耗时: " << ms << " ms\n";
    }
    return true;
}

// 多线程并行处理
void process_all_files_parallel(const std::string& input_dir,
                                const std::string& output_base,
                                int az_min, int az_max,
                                const ProcessingOptions& opts,
                                int threads = 4)
{
    if (!fs::exists(input_dir)) {
        std::cerr << "输入目录不存在: " << input_dir << std::endl;
        return;
    }
    fs::create_directories(output_base);

    // 收集 .bin 文件
    std::vector<std::string> files;
    for (auto& e : fs::directory_iterator(input_dir)) {
        if (e.path().extension() == ".bin")
            files.push_back(e.path().string());
    }
    if (files.empty()) {
        std::cout << "无 .bin 文件可处理\n";
        return;
    }
    std::sort(files.begin(), files.end());

    // 线程数
    unsigned hw = std::thread::hardware_concurrency();
    threads = std::min({threads, MAX_THREADS, static_cast<int>(hw?hw:2)});

    std::queue<std::string> q;
    for (auto& f : files) q.push(f);

    std::mutex mtx;
    std::atomic<size_t> done{0};

    auto worker = [&](){
        while (true) {
            std::string f;
            {
                std::lock_guard lk(mtx);
                if (q.empty()) break;
                f = q.front(); q.pop();
            }
            process_single_bin_file(f, az_min, az_max, output_base, opts);
            done++;
            if (opts.verbose_log) {
                std::lock_guard lk(mtx);
                std::cout << "进度: " << done << "/" << files.size()
                          << " - " << fs::path(f).filename() << "\n";
            }
        }
    };

    std::vector<std::thread> ths;
    for (int i = 0; i < threads; i++) ths.emplace_back(worker);
    for (auto& t: ths) if (t.joinable()) t.join();

    std::cout << "所有文件处理完毕，共 " << files.size() << " 个文件\n";
}

// 打印使用说明
void print_usage() {
    std::cout << "双通道数据保存程序\n";
    std::cout << "可选保存组件：\n";
    std::cout << "  SH - 和通道帧头 (40字节)\n";
    std::cout << "  SD - 和通道重排数据 (~16MB)\n";
    std::cout << "  DH - 差通道帧头 (40字节)\n";
    std::cout << "  DD - 差通道重排数据 (~16MB)\n";
    std::cout << "\n修改main函数中的save_opts配置来选择保存的组件\n";
}

int main() {
    print_usage();

    std::string in_dir     = "/home/<USER>/Downloads/bin_files";
    std::string out_base   = "/home/<USER>/Downloads/bin_files/out";
    int az_min = 0 * 100, az_max = 360 * 100;

    ProcessingOptions opts;
    opts.save_csv     = true;
    opts.verbose_log  = true;
    opts.io_buffer_size = 65536;

    // 配置保存选项 - 可以根据需要修改
    opts.save_opts.save_sum_header = true;   // 保存和通道帧头
    opts.save_opts.save_sum_data = true;     // 保存和通道重排数据
    opts.save_opts.save_diff_header = true;  // 保存差通道帧头
    opts.save_opts.save_diff_data = true;    // 保存差通道重排数据

    std::cout << "当前保存配置：\n";
    std::cout << "  和通道帧头: " << (opts.save_opts.save_sum_header ? "是" : "否") << "\n";
    std::cout << "  和通道数据: " << (opts.save_opts.save_sum_data ? "是" : "否") << "\n";
    std::cout << "  差通道帧头: " << (opts.save_opts.save_diff_header ? "是" : "否") << "\n";
    std::cout << "  差通道数据: " << (opts.save_opts.save_diff_data ? "是" : "否") << "\n\n";

    unsigned hw = std::thread::hardware_concurrency();
    int threads = hw? hw : 4;

    process_all_files_parallel(in_dir, out_base, az_min, az_max, opts, threads);
    return 0;
}
